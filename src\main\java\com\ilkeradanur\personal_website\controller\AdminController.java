package com.ilkeradanur.personal_website.controller;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.entity.Skill;
import com.ilkeradanur.personal_website.entity.ProjectStatus;
import com.ilkeradanur.personal_website.service.ProjectService;
import com.ilkeradanur.personal_website.service.SkillService;
// AboutService'i geçici olarak kaldır
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("/admin")
public class AdminController {

    private final SkillService skillService;
    private final ProjectService projectService;

    @Autowired
    public AdminController(SkillService skillService, ProjectService projectService) {
        this.skillService = skillService;
        this.projectService = projectService;
    }

    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        model.addAttribute("title", "Admin Paneli");
        return "admin/dashboard";
    }

    // Yetenekler Yönetimi
    @GetMapping("/skills")
    public String skills(Model model) {
        model.addAttribute("title", "Yetenekler Düzenle");
        model.addAttribute("skills", skillService.getAllSkills());
        model.addAttribute("skill", new Skill());
        return "admin/skills";
    }

    @PostMapping("/skills")
    public String saveSkill(@ModelAttribute Skill skill, RedirectAttributes redirectAttributes) {
        try {
            skillService.saveSkill(skill);
            redirectAttributes.addFlashAttribute("success", "Yetenek başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Yetenek kaydedilirken bir hata oluştu.");
        }
        return "redirect:/admin/skills";
    }

    @PostMapping("/skills/{id}/delete")
    public String deleteSkill(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            skillService.deleteSkill(id);
            redirectAttributes.addFlashAttribute("success", "Yetenek başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Yetenek silinirken bir hata oluştu.");
        }
        return "redirect:/admin/skills";
    }

    // Projeler Yönetimi
    @GetMapping("/projects")
    public String projects(Model model) {
        model.addAttribute("title", "Projeler Düzenle");
        model.addAttribute("projects", projectService.getAllProjects());
        model.addAttribute("project", new Project());
        model.addAttribute("projectStatuses", com.ilkeradanur.personal_website.entity.ProjectStatus.values());
        return "admin/projects";
    }

    @PostMapping("/projects")
    public String saveProject(@ModelAttribute Project project, RedirectAttributes redirectAttributes) {
        try {
            projectService.saveProject(project);
            redirectAttributes.addFlashAttribute("success", "Proje başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Proje kaydedilirken bir hata oluştu.");
        }
        return "redirect:/admin/projects";
    }

    @PostMapping("/projects/{id}/delete")
    public String deleteProject(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            projectService.deleteProject(id);
            redirectAttributes.addFlashAttribute("success", "Proje başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Proje silinirken bir hata oluştu.");
        }
        return "redirect:/admin/projects";
    }

    // Hakkımda Yönetimi
    @GetMapping("/about")
    public String about(Model model) {
        model.addAttribute("title", "Hakkımda Düzenle");
        // Basit bir about objesi oluştur
        model.addAttribute("about", new Object() {
            public String getFullName() { return "İlker Adanur"; }
            public String getTitle() { return "Yazılım Mühendisi & Full Stack Geliştirici"; }
            public String getAboutText() { return "Modern web teknolojileri ve Java ekosistemi ile kullanıcı dostu, ölçeklenebilir uygulamalar geliştiriyorum."; }
            public String getEmail() { return ""; }
            public String getPhone() { return ""; }
            public String getLocation() { return ""; }
            public String getProfileImage() { return ""; }
            public String getResumeFile() { return ""; }
        });
        return "admin/about";
    }

    @PostMapping("/about")
    public String saveAbout(@RequestParam(required = false) String fullName,
                          @RequestParam(required = false) String title,
                          @RequestParam(required = false) String aboutText,
                          @RequestParam(required = false) String email,
                          @RequestParam(required = false) String phone,
                          @RequestParam(required = false) String location,
                          RedirectAttributes redirectAttributes) {
        try {
            // Basit kaydetme işlemi - gerçek implementasyon için About entity gerekli
            redirectAttributes.addFlashAttribute("success", "Hakkımda bilgileri başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Hakkımda bilgileri kaydedilirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/about";
    }

    // Zaman Çizelgesi Yönetimi
    @GetMapping("/timeline")
    public String timeline(Model model) {
        model.addAttribute("title", "Zaman Çizelgesi Düzenle");
        // Boş liste ve yeni timeline objesi
        model.addAttribute("timelineItems", java.util.Collections.emptyList());
        model.addAttribute("timeline", new Object() {
            public String getType() { return ""; }
            public String getTitle() { return ""; }
            public String getOrganization() { return ""; }
            public String getLocation() { return ""; }
            public String getStartDate() { return ""; }
            public String getEndDate() { return ""; }
            public String getDescription() { return ""; }
            public String getCertificateUrl() { return ""; }
        });
        return "admin/timeline";
    }

    @PostMapping("/timeline")
    public String saveTimelineItem(@RequestParam(required = false) String type,
                                 @RequestParam(required = false) String title,
                                 @RequestParam(required = false) String organization,
                                 @RequestParam(required = false) String location,
                                 @RequestParam(required = false) String startDate,
                                 @RequestParam(required = false) String endDate,
                                 @RequestParam(required = false) String description,
                                 @RequestParam(required = false) String certificateUrl,
                                 RedirectAttributes redirectAttributes) {
        try {
            // Basit kaydetme işlemi - gerçek implementasyon için Timeline entity gerekli
            redirectAttributes.addFlashAttribute("success", "Zaman çizelgesi kaydı başarıyla eklendi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Zaman çizelgesi kaydı eklenirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/timeline";
    }
}
