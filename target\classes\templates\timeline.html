<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="İlker <PERSON>nur - <PERSON>">
    <meta name="keywords" content="<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>aman Çizelgesi, Yazılım Projeleri">
    <meta name="author" content="<PERSON>lk<PERSON>nur">

    <title th:text="${title}"><PERSON><PERSON><PERSON> - <PERSON>izelge<PERSON></title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">

    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <!-- Navigasyon -->
    <nav class="nav">
        <div class="nav__container container">
            <a href="/" class="nav__logo">İlker Adanur</a>

            <button class="nav__theme-toggle" aria-label="Tema değiştir">
                <i class="fas fa-moon"></i>
            </button>

            <ul class="nav__menu">
                <li><a href="/#about" class="nav__link">Hakkımda</a></li>
                <li><a href="/skills" class="nav__link">Yetenekler</a></li>
                <li><a href="/projects" class="nav__link">Projeler</a></li>
                <li><a href="/timeline" class="nav__link nav__link--active">Zaman Çizelgesi</a></li>
                <li><a href="/contact" class="nav__link">İletişim</a></li>
                <li sec:authorize="!isAuthenticated()"><a href="/login" class="nav__link nav__link--admin">Yetkili Girişi</a></li>
                <li sec:authorize="isAuthenticated()"><a href="/admin/dashboard" class="nav__link nav__link--admin">Admin Paneli</a></li>
            </ul>
        </div>
    </nav>

    <!-- Zaman Çizelgesi Bölümü -->
    <section class="timeline-section" id="timeline">
        <div class="container">
            <div class="timeline-section__header text-center">
                <h1 th:text="${title}">Proje Zaman Çizelgesi</h1>
                <p class="text-secondary">Hayatımın önemli dönemleri ve projelerim</p>
            </div>

            <div class="timeline">
                <div class="timeline__period" th:each="project : ${projects}">
                    <div class="timeline__period-header">
                        <h2 th:text="${project.title}">Proje Başlığı</h2>
                        <p class="timeline__period-subtitle" th:text="${project.category}">Kategori</p>
                        <p class="timeline__period-date" th:if="${project.startDate != null and !project.startDate.isEmpty()}">
                            <span th:text="${project.startDate}">Başlangıç Tarihi</span>
                            <span th:if="${project.endDate != null and !project.endDate.isEmpty()}" th:text="' - ' + ${project.endDate}">Bitiş Tarihi</span>
                        </p>
                    </div>

                    <div class="timeline__projects">
                        <div class="project-card" th:data-category="${project.category}" th:data-technologies="${project.technologies}" th:data-status="${project.status}">
                            <div class="project-card__header">
                                <i class="fas" th:classappend="${project.category == 'web' ? 'fa-globe' :
                                                              project.category == 'mobile' ? 'fa-mobile-alt' :
                                                              project.category == 'backend' ? 'fa-server' :
                                                              project.category == 'desktop' ? 'fa-desktop' :
                                                              project.category == 'ai' ? 'fa-robot' : 'fa-code'}"></i>
                                <h3 th:text="${project.title}">Proje Başlığı</h3>
                            </div>
                            <p class="project-card__description" th:text="${project.description}">
                                Proje açıklaması
                            </p>
                            <div class="project-card__tags">
                                <span class="tag" th:each="tech : ${project.technologies.split(',')}" th:text="${tech.trim()}">Teknoloji</span>
                            </div>
                            <div class="project-card__links" th:if="${project.githubLink != null || project.demoLink != null}">
                                <a th:if="${project.githubLink != null}" th:href="${project.githubLink}" class="project-card__link" target="_blank" rel="noopener">
                                    <i class="fab fa-github"></i> GitHub
                                </a>
                                <a th:if="${project.demoLink != null}" th:href="${project.demoLink}" class="project-card__link" target="_blank" rel="noopener">
                                    <i class="fas fa-external-link-alt"></i> Demo
                                </a>
                            </div>
                            <div class="project-card__status" th:classappend="${project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).COMPLETED ? 'status-completed' :
                                                                           project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).IN_PROGRESS ? 'status-in-progress' :
                                                                           project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).ON_HOLD ? 'status-on-hold' : 'status-cancelled'}">
                                <span th:text="${project.status.displayName}">Durum</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__social">
                    <a href="https://github.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://linkedin.com/in/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://twitter.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
                <p>&copy; 2024 İlker Adanur. Tüm hakları saklıdır.</p>
            </div>
        </div>

        <button class="footer__back-to-top" aria-label="Yukarı çık">
            <i class="fas fa-arrow-up"></i>
        </button>
    </footer>

    <!-- JavaScript -->
    <script src="/js/main.js"></script>
</body>
</html>