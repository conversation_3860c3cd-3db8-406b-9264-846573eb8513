<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${title}">Yetenekler Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Yetenekler Yönetimi</h1>
                    <a href="/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
                    </a>
                </div>

                <!-- Bildirimler -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Yetenek Ekleme Formu -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Yeni Yetenek Ekle</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/skills}" method="post" th:object="${skill}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Yetenek Adı</label>
                                    <input type="text" class="form-control" id="name" th:field="*{name}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">Kategori</label>
                                    <select class="form-select" id="category" th:field="*{category}" required>
                                        <option value="Programlama Dilleri">Programlama Dilleri</option>
                                        <option value="Web Teknolojileri">Web Teknolojileri</option>
                                        <option value="Veritabanları">Veritabanları</option>
                                        <option value="DevOps">DevOps</option>
                                        <option value="Mobil Geliştirme">Mobil Geliştirme</option>
                                        <option value="Diğer">Diğer</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="proficiency" class="form-label">Yeterlilik (%)</label>
                                    <input type="number" class="form-control" id="proficiency" th:field="*{proficiency}" min="0" max="100" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="icon" class="form-label">İkon (Font Awesome sınıfı)</label>
                                    <input type="text" class="form-control" id="icon" th:field="*{icon}" placeholder="Örn: fab fa-java">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="certificationName" class="form-label">Sertifika Adı</label>
                                    <input type="text" class="form-control" id="certificationName" th:field="*{certificationName}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="certificationUrl" class="form-label">Sertifika URL</label>
                                    <input type="url" class="form-control" id="certificationUrl" th:field="*{certificationUrl}">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Kaydet
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Yetenekler Listesi -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Mevcut Yetenekler</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>İkon</th>
                                        <th>Yetenek</th>
                                        <th>Kategori</th>
                                        <th>Yeterlilik</th>
                                        <th>Sertifika</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="skill : ${skills}">
                                        <td>
                                            <i th:class="${skill.icon}" class="skill-icon"></i>
                                        </td>
                                        <td th:text="${skill.name}"></td>
                                        <td th:text="${skill.category}"></td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar"
                                                     th:style="'width: ' + ${skill.proficiency} + '%'"
                                                     th:text="${skill.proficiency} + '%'">
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a th:if="${skill.certificationUrl}"
                                               th:href="${skill.certificationUrl}"
                                               target="_blank"
                                               th:text="${skill.certificationName}">
                                            </a>
                                        </td>
                                        <td>
                                            <form th:action="@{/admin/skills/{id}/delete(id=${skill.id})}"
                                                  method="post"
                                                  style="display: inline;"
                                                  onsubmit="return confirm('Bu yeteneği silmek istediğinizden emin misiniz?');">
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>