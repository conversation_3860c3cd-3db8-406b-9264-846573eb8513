<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${title}"><PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><PERSON><PERSON>ler <PERSON>ö<PERSON>im<PERSON></h1>
                    <a href="/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
                    </a>
                </div>

                <!-- Bildir<PERSON>ler -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Proje Ekleme Formu -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Yeni Proje Ekle</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/projects}" method="post" th:object="${project}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">Proje Başlığı</label>
                                    <input type="text" class="form-control" id="title" th:field="*{title}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">Kategori</label>
                                    <select class="form-select" id="category" th:field="*{category}" required>
                                        <option value="Web Geliştirme">Web Geliştirme</option>
                                        <option value="Mobil Geliştirme">Mobil Geliştirme</option>
                                        <option value="Backend">Backend</option>
                                        <option value="Masaüstü">Masaüstü</option>
                                        <option value="Veri Bilimi">Veri Bilimi</option>
                                        <option value="DevOps">DevOps</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="technologies" class="form-label">Teknolojiler (virgülle ayırın)</label>
                                    <input type="text" class="form-control" id="technologies" th:field="*{technologies}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">Durum</label>
                                    <select class="form-select" id="status" th:field="*{status}" required>
                                        <option th:each="status : ${projectStatuses}"
                                                th:value="${status}"
                                                th:text="${status.displayName}">
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="startDate" class="form-label">Başlangıç Tarihi</label>
                                    <input type="date" class="form-control" id="startDate" th:field="*{startDate}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="endDate" class="form-label">Bitiş Tarihi</label>
                                    <input type="date" class="form-control" id="endDate" th:field="*{endDate}">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="githubLink" class="form-label">GitHub Linki</label>
                                    <input type="url" class="form-control" id="githubLink" th:field="*{githubLink}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="demoLink" class="form-label">Demo Linki</label>
                                    <input type="url" class="form-control" id="demoLink" th:field="*{demoLink}">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Kaydet
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Projeler Listesi -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Mevcut Projeler</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Proje</th>
                                        <th>Kategori</th>
                                        <th>Teknolojiler</th>
                                        <th>Durum</th>
                                        <th>Tarih</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="project : ${projects}">
                                        <td>
                                            <strong th:text="${project.title}"></strong>
                                            <div class="small text-muted" th:text="${#strings.abbreviate(project.description, 100)}"></div>
                                        </td>
                                        <td th:text="${project.category}"></td>
                                        <td>
                                            <span th:each="tech : ${#strings.arraySplit(project.technologies, ',')}"
                                                  class="badge bg-secondary me-1"
                                                  th:text="${tech.trim()}">
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge"
                                                  th:classappend="${project.status.name() == 'COMPLETED' ? 'bg-success' :
                                                                 project.status.name() == 'IN_PROGRESS' ? 'bg-primary' :
                                                                 project.status.name() == 'ON_HOLD' ? 'bg-warning' : 'bg-danger'}"
                                                  th:text="${project.status.displayName}">
                                            </span>
                                        </td>
                                        <td>
                                            <div th:if="${project.startDate and !project.startDate.isEmpty()}" th:text="${project.startDate}"></div>
                                            <div th:if="${project.endDate and !project.endDate.isEmpty()}" th:text="${project.endDate}"></div>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a th:if="${project.githubLink}"
                                                   th:href="${project.githubLink}"
                                                   class="btn btn-sm btn-outline-dark"
                                                   target="_blank">
                                                    <i class="fab fa-github"></i>
                                                </a>
                                                <a th:if="${project.demoLink}"
                                                   th:href="${project.demoLink}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   target="_blank">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                                <form th:action="@{/admin/projects/{id}/delete(id=${project.id})}"
                                                      method="post"
                                                      style="display: inline;"
                                                      onsubmit="return confirm('Bu projeyi silmek istediğinizden emin misiniz?');">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>