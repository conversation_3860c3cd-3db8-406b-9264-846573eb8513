<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${title}"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Ha<PERSON><PERSON><PERSON><PERSON><PERSON> Düzenle</h1>
                    <a href="/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
                    </a>
                </div>

                <!-- Bildir<PERSON><PERSON> -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Hakkımda Düzenleme Formu -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Kişisel Bilgiler</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/about}" method="post">
                            <!-- Profil Fotoğrafı -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <img th:if="${about.profileImage}"
                                             th:src="${about.profileImage}"
                                             class="img-thumbnail rounded-circle mb-2"
                                             style="width: 200px; height: 200px; object-fit: cover;"
                                             alt="Profil Fotoğrafı">
                                        <img th:unless="${about.profileImage}"
                                             src="/images/default-profile.png"
                                             class="img-thumbnail rounded-circle mb-2"
                                             style="width: 200px; height: 200px; object-fit: cover;"
                                             alt="Varsayılan Profil Fotoğrafı">
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="mb-3">
                                        <label for="profileImage" class="form-label">Profil Fotoğrafı</label>
                                        <input type="file" class="form-control" id="profileImage" name="profileImage" accept="image/*">
                                        <div class="form-text">Önerilen boyut: 400x400 piksel. Maksimum dosya boyutu: 2MB</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Kişisel Bilgiler -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Ad Soyad</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">Ünvan</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">E-posta</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="location" class="form-label">Konum</label>
                                    <input type="text" class="form-control" id="location" name="location">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="description" class="form-label">Hakkımda</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                </div>
                            </div>



                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Değişiklikleri Kaydet
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>