package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.User;
import com.ilkeradanur.personal_website.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class CustomUserDetailsService implements UserDetailsService {

    private final UserRepository userRepository;

    @Autowired
    public CustomUserDetailsService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        System.out.println("DEBUG: <PERSON>nan kullanıcı adı: " + username);

        // Önce tüm kullanıcıları listele
        List<User> allUsers = userRepository.findAll();
        System.out.println("DEBUG: Toplam kullanıcı sayısı: " + allUsers.size());
        for (User u : allUsers) {
            if (u != null) {
                System.out.println("DEBUG: Kullanıcı - ID: " + u.getId() + ", Username: '" + u.getUsername() + "', Password: '" + u.getPassword() + "', Role: '" + u.getRole() + "', Enabled: " + u.isEnabled());
            } else {
                System.out.println("DEBUG: NULL kullanıcı bulundu!");
            }
        }

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> {
                    System.out.println("DEBUG: Kullanıcı bulunamadı: " + username);
                    return new UsernameNotFoundException("Kullanıcı bulunamadı: " + username);
                });

        System.out.println("DEBUG: Bulunan kullanıcı - Username: " + user.getUsername() + ", Password: " + user.getPassword() + ", Role: " + user.getRole() + ", Enabled: " + user.isEnabled());

        return org.springframework.security.core.userdetails.User
                .withUsername(user.getUsername())
                .password(user.getPassword())
                .disabled(!user.isEnabled())
                .accountExpired(false)
                .accountLocked(false)
                .credentialsExpired(false)
                .authorities(Collections.singletonList(new SimpleGrantedAuthority(user.getRole())))
                .build();
    }
}
