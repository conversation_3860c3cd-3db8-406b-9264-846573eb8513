package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.entity.ProjectStatus;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ProjectService {
    // Temel CRUD işlemleri
    List<Project> getAllProjects();
    Project saveProject(Project project);
    void deleteProject(Long id);

    // Proje durumuna göre işlemler
    List<Project> getProjectsByStatus(ProjectStatus status);

    // Kategori işlemleri
    List<Project> getProjectsByCategory(String category);
    Map<String, Long> getProjectCountByCategory();

    // <PERSON><PERSON>h bazlı işlemler
    List<Project> getProjectsAfterDate(String date);
    List<Project> getProjectsBetweenDates(String startDate, String endDate);

    // Teknoloji bazlı işlemler
    List<Project> getProjectsByTechnology(String technology);
    List<Project> getProjectsByTechnologies(Set<String> technologies);
    List<Project> getProjectsByCategoryAndTechnology(String category, String technology);
    Map<String, Long> getProjectCountByTechnology();
    Set<String> getAllTechnologies();

    // Özel sorgular
    List<Project> getActiveProjects();
    List<Project> getLatestProjects();
}