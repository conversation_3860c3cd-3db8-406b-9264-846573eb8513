<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="İlker Adanur - Yetenekler">
    <meta name="keywords" content="İlker Adanur, Yetenekler, Yazılım Mühendisi, Programlama, Sertifikalar">
    <meta name="author" content="<PERSON>lk<PERSON> Adanur">

    <title th:text="${title}"><PERSON><PERSON><PERSON> Adanur - Yetenekler</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">

    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <!-- Navigasyon -->
    <nav class="nav">
        <div class="nav__container container">
            <a href="/" class="nav__logo">İlker Adanur</a>

            <button class="nav__theme-toggle" aria-label="Tema değiştir">
                <i class="fas fa-moon"></i>
            </button>

            <ul class="nav__menu">
                <li><a href="/#about" class="nav__link">Hakkımda</a></li>
                <li><a href="/skills" class="nav__link nav__link--active">Yetenekler</a></li>
                <li><a href="/projects" class="nav__link">Projeler</a></li>
                <li><a href="/timeline" class="nav__link">Zaman Çizelgesi</a></li>
                <li><a href="/contact" class="nav__link">İletişim</a></li>
                <li sec:authorize="!isAuthenticated()"><a href="/login" class="nav__link nav__link--admin">Yetkili Girişi</a></li>
                <li sec:authorize="isAuthenticated()"><a href="/admin/dashboard" class="nav__link nav__link--admin">Admin Paneli</a></li>
            </ul>
        </div>
    </nav>

    <!-- Yetenekler Bölümü -->
    <section class="skills-section">
        <div class="container">
            <div class="skills-section__header text-center">
                <h1 th:text="${title}">Yeteneklerim</h1>
                <p class="text-secondary">Teknik yeteneklerim ve sertifikalarım</p>
            </div>

            <div class="skills__filters mb-4">
                <button class="filter__button filter__button--active" data-filter="all">Tümü</button>
                <button class="filter__button" data-filter="programming">Programlama</button>
                <button class="filter__button" data-filter="framework">Framework</button>
                <button class="filter__button" data-filter="database">Veritabanı</button>
                <button class="filter__button" data-filter="tool">Araç</button>
                <button class="filter__button" data-filter="language">Dil</button>
                <button class="filter__button" data-filter="certification">Sertifika</button>
            </div>

            <div class="skills__grid">
                <div class="skill-card" th:each="skill : ${skills}"
                     th:data-category="${skill.category}"
                     th:data-proficiency="${skill.proficiency}">
                    <div class="skill-card__header">
                        <i th:class="${skill.icon}" class="skill-card__icon"></i>
                        <h3 class="skill-card__name" th:text="${skill.name}">Yetenek Adı</h3>
                    </div>

                    <div class="skill-card__progress">
                        <div class="skill-card__bar" th:style="'width: ' + ${skill.proficiency} + '%'"></div>
                        <span class="skill-card__percentage" th:text="${skill.proficiency} + '%'">90%</span>
                    </div>

                    <p class="skill-card__description" th:if="${skill.description != null}" th:text="${skill.description}">
                        Yetenek açıklaması
                    </p>

                    <div class="skill-card__certification" th:if="${skill.certificationName != null}">
                        <h4>Sertifika Bilgileri</h4>
                        <ul>
                            <li>
                                <strong>Sertifika:</strong>
                                <a th:href="${skill.certificationUrl}" target="_blank" rel="noopener"
                                   th:text="${skill.certificationName}">Sertifika Adı</a>
                            </li>
                            <li th:if="${skill.issuer != null}">
                                <strong>Veren Kurum:</strong>
                                <span th:text="${skill.issuer}">Kurum Adı</span>
                            </li>
                            <li th:if="${skill.issueDate != null}">
                                <strong>Verilme Tarihi:</strong>
                                <span th:text="${skill.issueDate}">Tarih</span>
                            </li>
                            <li th:if="${skill.expiryDate != null}">
                                <strong>Geçerlilik Tarihi:</strong>
                                <span th:text="${skill.expiryDate}">Tarih</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="skills__stats mt-4">
                <h3 class="text-center mb-3">Yetenek İstatistikleri</h3>
                <div class="stats__grid">
                    <div class="stats__card" th:each="stat : ${skillCategoryStats}">
                        <h4 th:text="${stat.key}">Kategori</h4>
                        <p class="stats__number" th:text="${stat.value}">0</p>
                        <p class="stats__label">Yetenek</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__social">
                    <a href="https://github.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://www.linkedin.com/in/ilker-adanur-software-engineer/" class="social__link" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://x.com/_ilkerAdanur" class="social__link" target="_blank" rel="noopener" aria-label="X">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
                <p>&copy; 2024 İlker Adanur. Tüm hakları saklıdır.</p>
            </div>
        </div>

        <button class="footer__back-to-top" aria-label="Yukarı çık">
            <i class="fas fa-arrow-up"></i>
        </button>
    </footer>

    <!-- JavaScript -->
    <script th:inline="javascript">
        // Thymeleaf değişkenlerini JavaScript'e aktar
        const skills = /*[[${skills}]]*/ [];

        document.addEventListener('DOMContentLoaded', function() {
            // Tema değiştirme
            const themeToggle = document.querySelector('.nav__theme-toggle');
            const body = document.body;

            themeToggle.addEventListener('click', () => {
                body.dataset.theme = body.dataset.theme === 'dark' ? 'light' : 'dark';
                themeToggle.querySelector('i').classList.toggle('fa-moon');
                themeToggle.querySelector('i').classList.toggle('fa-sun');
            });

            // Yetenek filtreleme
            const filterButtons = document.querySelectorAll('.filter__button');
            const skillCards = document.querySelectorAll('.skill-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    filterButtons.forEach(btn => btn.classList.remove('filter__button--active'));
                    this.classList.add('filter__button--active');

                    const filter = this.dataset.filter;

                    skillCards.forEach(card => {
                        if (filter === 'all' || card.dataset.category === filter) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Yukarı çık butonu
            const backToTopButton = document.querySelector('.footer__back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 100) {
                    backToTopButton.classList.add('footer__back-to-top--visible');
                } else {
                    backToTopButton.classList.remove('footer__back-to-top--visible');
                }
            });

            backToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>